import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:gp_core_v2/base/bloc/base_event.dart';

final class TestEvent extends CoreV2BaseEvent {
  const TestEvent();
}

final class TestCounterEvent extends CoreV2BaseEvent {
  const TestCounterEvent(this.counter);

  final int counter;

  @override
  List<Object?> get props => [counter];
}

final class AuthEmailCheck extends CoreV2BaseEvent {
  const AuthEmailCheck(this.authCheckEmailRequest);

  final AuthCheckEmailRequest authCheckEmailRequest;
}

final class AuthEmailCheckWithRunCatching extends CoreV2BaseEvent {
  const AuthEmailCheckWithRunCatching(this.authCheckEmailRequest);

  final AuthCheckEmailRequest authCheckEmailRequest;
}

final class TestError extends CoreV2BaseEvent {
  const TestError();
}

final class TestErrorWithCatching extends CoreV2BaseEvent {
  const TestErrorWithCatching();
}
