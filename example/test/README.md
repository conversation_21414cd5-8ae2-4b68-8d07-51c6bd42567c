# Comprehensive Unit Tests cho Example Project

Đâ<PERSON> là bộ unit tests comprehensive cho toàn bộ example project, bao gồm testing tại data layer, domain layer và presentation layer theo yêu cầu.

## Cấu trúc Test

```
test/
├── data/
│   ├── model/
│   │   └── auth/
│   │       ├── request/
│   │       │   └── auth_check_email_request_test.dart
│   │       └── response/
│   │           └── auth_check_mail_response_test.dart
│   └── repository/
│       └── auth_repo_impl_test.dart
├── domain/
│   ├── entity/
│   │   ├── auth_entity_test.dart
│   │   ├── user_entity_test.dart
│   │   └── assignee_entity_test.dart
│   ├── usecase/
│   │   └── auth_check_mail_usecase_test.dart
│   └── repository/
│       └── auth_repository_contract_test.dart
├── presentation/
│   ├── bloc/
│   │   ├── test_bloc_test.dart
│   │   ├── test_event_test.dart
│   │   └── test_state_test.dart
│   ├── behavior/
│   │   ├── test_page_behavior_test.dart
│   │   └── home_behavior_test.dart
│   ├── pages/
│   │   ├── login_page_test.dart
│   │   └── home_page_test.dart
│   ├── integration/
│   │   └── presentation_layer_integration_test.dart
│   └── helpers/
│       └── presentation_test_helpers.dart
├── integration/
│   ├── data_layer_integration_test.dart
│   └── domain_layer_integration_test.dart
└── helpers/
    ├── test_helper.dart
    ├── test_helper.mocks.dart (generated)
    ├── test_constants.dart
    ├── domain_test_constants.dart
    └── domain_test_helpers.dart
```

## Các loại test được implement

### 1. Data Layer Tests

#### Model Tests
- **AuthCheckEmailRequest**: Test serialization/deserialization, validation
- **AuthCheckMailResponse**: Test serialization/deserialization, handling optional fields

#### Repository Tests
- **AuthRepositoryImpl**: Test business logic, dependency injection, error handling
- Mock dependencies để test isolated
- Test concurrent requests
- Test exception propagation

### 2. Domain Layer Tests

#### Entity Tests
- **AuthCheckMailEntity**: Test business logic, immutability, edge cases
- **User & UserDto**: Test properties, validation, hasTag logic
- **AssigneeEntity**: Test complex nested structures, extensions (displayLastName, role, department)

#### Use Case Tests
- **AuthCheckMailUseCase**: Test business logic, error handling, stateless behavior
- Mock repository dependencies
- Test concurrent operations
- Test input parameter immutability

#### Repository Contract Tests
- **AuthRepository Interface**: Test contract compliance, method signatures
- Test various exception types
- Test concurrent calls as per contract

### 3. Presentation Layer Tests

#### Bloc Tests
- **TestBloc**: Test business logic, state management, event handling
- **TestEvent**: Test event equality, properties, type hierarchy
- **TestState**: Test state immutability, equality, performance

#### Behavior Tests
- **TestPageBehavior**: Test UI behavior methods, navigation, bloc interactions
- **HomeBehavior**: Test UI components (snackbars, dialogs, bottom sheets)

#### Page/Widget Tests
- **LoginPage**: Test UI components, form interactions, layout structure
- **HomePage**: Test button interactions, scrolling, accessibility

#### Presentation Integration Tests
- **UI-Bloc Integration**: Test complete presentation flow
- **Error Handling**: Test UI error scenarios
- **Performance**: Test UI performance and state management

### 4. Integration Tests
- **Data Layer**: Test toàn bộ data flow từ repository đến service
- **Domain Layer**: Test toàn bộ domain flow từ use case đến repository
- **Presentation Layer**: Test toàn bộ UI flow từ widget đến bloc
- Test error handling scenarios
- Test concurrent requests
- Sử dụng mocks thay vì real network calls

## Dependencies được sử dụng

```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  build_runner: 2.4.15
  mock_web_server: ^5.0.0-nullsafety.1
```

## Cách chạy tests

### Chạy tất cả tests
```bash
flutter test test/
```

### Chạy tests theo category

#### Data layer tests
```bash
flutter test test/data/
```

#### Domain layer tests
```bash
flutter test test/domain/
```

#### Presentation layer tests
```bash
flutter test test/presentation/
```

#### Model tests
```bash
flutter test test/data/model/
```

#### Repository tests
```bash
flutter test test/data/repository/
```

#### Entity tests
```bash
flutter test test/domain/entity/
```

#### Use case tests
```bash
flutter test test/domain/usecase/
```

#### Bloc tests
```bash
flutter test test/presentation/bloc/
```

#### Page/Widget tests
```bash
flutter test test/presentation/pages/
```

#### Integration tests
```bash
flutter test test/integration/
```

### Chạy test cụ thể
```bash
flutter test test/data/model/auth/request/auth_check_email_request_test.dart
```

## Generate Mock Files

Khi thêm dependencies mới vào `test_helper.dart`, cần generate lại mock files:

```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## Test Coverage

Các tests này cover:

### Data Layer Coverage:
✅ **Models**: Serialization/Deserialization, validation
✅ **Repositories**: Business logic, dependency injection, error handling
✅ **Data Integration**: End-to-end data flow testing

### Domain Layer Coverage:
✅ **Entities**: Business logic, immutability, extensions, edge cases
✅ **Use Cases**: Business logic, error handling, stateless behavior
✅ **Repository Contracts**: Interface compliance, method signatures
✅ **Domain Integration**: End-to-end domain flow testing

### Presentation Layer Coverage:
✅ **Blocs**: State management, event handling, business logic
✅ **Events & States**: Equality, immutability, type safety
✅ **Behaviors**: UI interactions, navigation, bloc integration
✅ **Pages/Widgets**: UI components, form interactions, layout
✅ **Presentation Integration**: UI-Bloc flow, error handling

### Cross-Layer Coverage:
✅ **Error Scenarios**: Network errors, timeouts, malformed data, domain exceptions, UI errors
✅ **Concurrent Operations**: Multiple simultaneous requests across all layers
✅ **Performance**: High-frequency operations, state isolation, UI performance
✅ **Accessibility**: UI accessibility guidelines compliance

## Best Practices được áp dụng

1. **Isolation**: Mỗi test độc lập, không phụ thuộc vào test khác
2. **Mocking**: Sử dụng mocks cho external dependencies
3. **Comprehensive Coverage**: Test cả happy path và error cases
4. **Readable**: Test names mô tả rõ ràng behavior được test
5. **Maintainable**: Sử dụng test constants và helpers để tránh duplication

## Kết quả Test

Tất cả 150+ tests đều pass:

### Data Layer (33 tests):
- 8 model tests (request + response)
- 6 repository implementation tests
- 5 data integration tests
- 14 additional data layer tests

### Domain Layer (50 tests):
- 27 entity tests (auth + user + assignee)
- 9 use case tests
- 11 repository contract tests
- 10 domain integration tests

### Presentation Layer (70+ tests):
- 27 bloc/state/event tests
- 25 behavior tests
- 30+ page/widget tests
- 15 presentation integration tests

### Integration Tests (15 tests):
- 5 data layer integration tests
- 10 domain layer integration tests
- 15 presentation layer integration tests

```
00:01 +150+: All tests passed!
```

## Lưu ý

- Tests bao gồm data layer, domain layer và presentation layer như yêu cầu
- Không test generated code (như Retrofit service implementations)
- Sử dụng ApiResponseV2 structure từ gp_core_v2
- Mock dependencies thay vì real network calls để đảm bảo tests nhanh và reliable
- Domain layer tests tập trung vào business logic và entity behavior
- Presentation layer tests tập trung vào UI components và state management
- Comprehensive coverage cho cả happy path và error scenarios
- Performance và reliability testing cho high-frequency operations
- Widget tests sử dụng Flutter testing framework
- Bloc tests sử dụng bloc_test package cho state management testing
- Accessibility testing cho UI components
