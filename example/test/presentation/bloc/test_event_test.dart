import 'package:flutter_test/flutter_test.dart';
import 'package:example/presentation/test/bloc/test_event.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';

import '../../helpers/test_constants.dart';

void main() {
  group('TestEvent', () {
    test('should create TestEvent instance', () {
      const event = TestEvent();
      expect(event, isA<TestEvent>());
    });

    test('should be equal to another TestEvent', () {
      const event1 = TestEvent();
      const event2 = TestEvent();
      expect(event1, equals(event2));
    });

    test('should have consistent hashCode', () {
      const event1 = TestEvent();
      const event2 = TestEvent();
      expect(event1.hashCode, equals(event2.hashCode));
    });

    test('should have empty props list', () {
      const event = TestEvent();
      expect(event.props, isEmpty);
    });
  });

  group('TestCounterEvent', () {
    const testCounter = 100;

    test('should create TestCounterEvent with counter value', () {
      const event = TestCounterEvent(testCounter);
      expect(event.counter, testCounter);
    });

    test('should be equal when counter values are same', () {
      const event1 = TestCounterEvent(testCounter);
      const event2 = TestCounterEvent(testCounter);
      expect(event1, equals(event2));
    });

    test('should not be equal when counter values are different', () {
      const event1 = TestCounterEvent(100);
      const event2 = TestCounterEvent(200);
      expect(event1, isNot(equals(event2)));
    });

    test('should have consistent hashCode for same counter values', () {
      const event1 = TestCounterEvent(testCounter);
      const event2 = TestCounterEvent(testCounter);
      expect(event1.hashCode, equals(event2.hashCode));
    });

    test('should have different hashCode for different counter values', () {
      const event1 = TestCounterEvent(100);
      const event2 = TestCounterEvent(200);
      expect(event1.hashCode, isNot(equals(event2.hashCode)));
    });

    test('should include counter in props', () {
      const event = TestCounterEvent(testCounter);
      expect(event.props, contains(testCounter));
    });

    test('should handle zero counter value', () {
      const event = TestCounterEvent(0);
      expect(event.counter, 0);
      expect(event.props, contains(0));
    });

    test('should handle negative counter value', () {
      const event = TestCounterEvent(-1);
      expect(event.counter, -1);
      expect(event.props, contains(-1));
    });

    test('should handle large counter value', () {
      const largeValue = 999999999;
      const event = TestCounterEvent(largeValue);
      expect(event.counter, largeValue);
      expect(event.props, contains(largeValue));
    });
  });

  group('AuthEmailCheck', () {
    late AuthCheckEmailRequest testRequest;

    setUp(() {
      testRequest = TestConstants.testAuthCheckEmailRequest;
    });

    test('should create AuthEmailCheck with request', () {
      final event = AuthEmailCheck(testRequest);
      expect(event.authCheckEmailRequest, testRequest);
    });

    test('should be equal when requests are same', () {
      final event1 = AuthEmailCheck(testRequest);
      final event2 = AuthEmailCheck(testRequest);
      expect(event1, equals(event2));
    });

    test('should not be equal when requests are different', () {
      final request1 = AuthCheckEmailRequest('<EMAIL>', '+84111111111');
      final request2 = AuthCheckEmailRequest('<EMAIL>', '+84222222222');
      final event1 = AuthEmailCheck(request1);
      final event2 = AuthEmailCheck(request2);
      expect(event1, isNot(equals(event2)));
    });

    test('should have empty props list (no props override)', () {
      final event = AuthEmailCheck(testRequest);
      expect(event.props, isEmpty);
    });

    test('should handle different email formats', () {
      final testCases = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (final email in testCases) {
        final request = AuthCheckEmailRequest(email, '+84123456789');
        final event = AuthEmailCheck(request);
        expect(event.authCheckEmailRequest.email, email);
      }
    });

    test('should handle different phone number formats', () {
      final testCases = [
        '+84123456789',
        '+1234567890',
        '+44123456789',
        '+81123456789',
      ];

      for (final phone in testCases) {
        final request = AuthCheckEmailRequest('<EMAIL>', phone);
        final event = AuthEmailCheck(request);
        expect(event.authCheckEmailRequest.phoneNumber, phone);
      }
    });
  });

  group('AuthEmailCheckWithRunCatching', () {
    late AuthCheckEmailRequest testRequest;

    setUp(() {
      testRequest = TestConstants.testAuthCheckEmailRequest;
    });

    test('should create AuthEmailCheckWithRunCatching with request', () {
      final event = AuthEmailCheckWithRunCatching(testRequest);
      expect(event.authCheckEmailRequest, testRequest);
    });

    test('should be equal when requests are same', () {
      final event1 = AuthEmailCheckWithRunCatching(testRequest);
      final event2 = AuthEmailCheckWithRunCatching(testRequest);
      expect(event1, equals(event2));
    });

    test('should not be equal when requests are different', () {
      final request1 = AuthCheckEmailRequest('<EMAIL>', '+84111111111');
      final request2 = AuthCheckEmailRequest('<EMAIL>', '+84222222222');
      final event1 = AuthEmailCheckWithRunCatching(request1);
      final event2 = AuthEmailCheckWithRunCatching(request2);
      expect(event1, isNot(equals(event2)));
    });

    test('should have empty props list (no props override)', () {
      final event = AuthEmailCheckWithRunCatching(testRequest);
      expect(event.props, isEmpty);
    });

    test('should be different from AuthEmailCheck with same request', () {
      final authEmailCheck = AuthEmailCheck(testRequest);
      final authEmailCheckWithRunCatching = AuthEmailCheckWithRunCatching(testRequest);
      expect(authEmailCheck, isNot(equals(authEmailCheckWithRunCatching)));
    });
  });

  group('TestError', () {
    test('should create TestError instance', () {
      const event = TestError();
      expect(event, isA<TestError>());
    });

    test('should be equal to another TestError', () {
      const event1 = TestError();
      const event2 = TestError();
      expect(event1, equals(event2));
    });

    test('should have consistent hashCode', () {
      const event1 = TestError();
      const event2 = TestError();
      expect(event1.hashCode, equals(event2.hashCode));
    });

    test('should have empty props list', () {
      const event = TestError();
      expect(event.props, isEmpty);
    });
  });

  group('TestErrorWithCatching', () {
    test('should create TestErrorWithCatching instance', () {
      const event = TestErrorWithCatching();
      expect(event, isA<TestErrorWithCatching>());
    });

    test('should be equal to another TestErrorWithCatching', () {
      const event1 = TestErrorWithCatching();
      const event2 = TestErrorWithCatching();
      expect(event1, equals(event2));
    });

    test('should have consistent hashCode', () {
      const event1 = TestErrorWithCatching();
      const event2 = TestErrorWithCatching();
      expect(event1.hashCode, equals(event2.hashCode));
    });

    test('should have empty props list', () {
      const event = TestErrorWithCatching();
      expect(event.props, isEmpty);
    });

    test('should be different from TestError', () {
      const testError = TestError();
      const testErrorWithCatching = TestErrorWithCatching();
      expect(testError, isNot(equals(testErrorWithCatching)));
    });
  });

  group('Event Type Hierarchy', () {
    test('all events should extend CoreV2BaseEvent', () {
      const testEvent = TestEvent();
      const testCounterEvent = TestCounterEvent(100);
      final authEmailCheck = AuthEmailCheck(TestConstants.testAuthCheckEmailRequest);
      final authEmailCheckWithRunCatching = AuthEmailCheckWithRunCatching(TestConstants.testAuthCheckEmailRequest);
      const testError = TestError();
      const testErrorWithCatching = TestErrorWithCatching();

      expect(testEvent, isA<Object>());
      expect(testCounterEvent, isA<Object>());
      expect(authEmailCheck, isA<Object>());
      expect(authEmailCheckWithRunCatching, isA<Object>());
      expect(testError, isA<Object>());
      expect(testErrorWithCatching, isA<Object>());
    });

    test('events should have different runtime types', () {
      const testEvent = TestEvent();
      const testCounterEvent = TestCounterEvent(100);
      final authEmailCheck = AuthEmailCheck(TestConstants.testAuthCheckEmailRequest);
      const testError = TestError();

      expect(testEvent.runtimeType, isNot(equals(testCounterEvent.runtimeType)));
      expect(testEvent.runtimeType, isNot(equals(authEmailCheck.runtimeType)));
      expect(testEvent.runtimeType, isNot(equals(testError.runtimeType)));
      expect(testCounterEvent.runtimeType, isNot(equals(authEmailCheck.runtimeType)));
    });
  });
}
