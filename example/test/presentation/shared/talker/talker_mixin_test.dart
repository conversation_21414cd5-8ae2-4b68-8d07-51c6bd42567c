import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:example/presentation/shared/talker/talker_mixin.dart';

// Generate mocks
@GenerateMocks([Talker, NavigatorObserver])
import 'talker_mixin_test.mocks.dart';

// Test widget that uses TalkerMixin
class TestWidgetWithTalkerMixin extends StatelessWidget with TalkerMixin {
  const TestWidgetWithTalkerMixin({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Test')),
      body: ElevatedButton(
        onPressed: () => navigateToTalkerScreen(context),
        child: const Text('Open Talker'),
      ),
    );
  }
}

void main() {
  group('TalkerMixin', () {
    late MockTalker mockTalker;
    late MockNavigatorObserver mockNavigatorObserver;

    setUp(() {
      mockTalker = MockTalker();
      mockNavigatorObserver = MockNavigatorObserver();
      
      // Reset GetIt
      if (GetIt.I.isRegistered<Talker>()) {
        GetIt.I.unregister<Talker>();
      }
    });

    tearDown(() {
      // Clean up GetIt
      if (GetIt.I.isRegistered<Talker>()) {
        GetIt.I.unregister<Talker>();
      }
    });

    group('navigateToTalkerScreen', () {
      testWidgets('should navigate to TalkerScreen when Talker is registered', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
            navigatorObservers: [mockNavigatorObserver],
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockNavigatorObserver.didPush(any, any)).called(2); // Initial route + TalkerScreen
        expect(find.byType(TalkerScreen), findsOneWidget);
      });

      testWidgets('should not navigate when Talker is not registered', (WidgetTester tester) async {
        // Arrange - Don't register Talker
        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
            navigatorObservers: [mockNavigatorObserver],
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockNavigatorObserver.didPush(any, any)).called(1); // Only initial route
        expect(find.byType(TalkerScreen), findsNothing);
      });

      testWidgets('should handle multiple navigation attempts', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
            navigatorObservers: [mockNavigatorObserver],
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();
        
        // Go back
        await tester.pageBack();
        await tester.pumpAndSettle();
        
        // Navigate again
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockNavigatorObserver.didPush(any, any)).called(3); // Initial + TalkerScreen + TalkerScreen again
        expect(find.byType(TalkerScreen), findsOneWidget);
      });

      testWidgets('should pass correct Talker instance to TalkerScreen', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        final talkerScreen = tester.widget<TalkerScreen>(find.byType(TalkerScreen));
        expect(talkerScreen.talker, equals(mockTalker));
      });

      testWidgets('should handle context correctly', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act & Assert - Should not throw
        expect(() async {
          await tester.tap(find.text('Open Talker'));
          await tester.pumpAndSettle();
        }, returnsNormally);
      });
    });

    group('Mixin Integration', () {
      testWidgets('should work with different widget types', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        // Test with StatelessWidget
        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(TalkerScreen), findsOneWidget);
      });

      testWidgets('should maintain widget functionality', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Assert - Widget should render normally
        expect(find.text('Test'), findsOneWidget);
        expect(find.text('Open Talker'), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      test('should implement TalkerBehavior interface', () {
        // Arrange & Act
        const widget = TestWidgetWithTalkerMixin();

        // Assert
        expect(widget, isA<TalkerMixin>());
        // Note: Can't directly test interface implementation in unit test
        // but the mixin declaration ensures it implements TalkerBehavior
      });
    });

    group('Error Handling', () {
      testWidgets('should handle GetIt exceptions gracefully', (WidgetTester tester) async {
        // Arrange - Register and then unregister to simulate error state
        GetIt.I.registerSingleton<Talker>(mockTalker);
        GetIt.I.unregister<Talker>();

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act & Assert - Should not throw
        expect(() async {
          await tester.tap(find.text('Open Talker'));
          await tester.pumpAndSettle();
        }, returnsNormally);

        // Should not navigate
        expect(find.byType(TalkerScreen), findsNothing);
      });

      testWidgets('should handle null context gracefully', (WidgetTester tester) async {
        // This test ensures the mixin doesn't crash with context issues
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act & Assert
        expect(() async {
          await tester.tap(find.text('Open Talker'));
          await tester.pumpAndSettle();
        }, returnsNormally);
      });
    });

    group('Performance', () {
      testWidgets('should handle rapid navigation attempts', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        final stopwatch = Stopwatch()..start();

        // Act - Rapid taps
        for (int i = 0; i < 5; i++) {
          await tester.tap(find.text('Open Talker'));
          await tester.pump(const Duration(milliseconds: 10));
          if (find.byType(TalkerScreen).evaluate().isNotEmpty) {
            await tester.pageBack();
            await tester.pump(const Duration(milliseconds: 10));
          }
        }

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      testWidgets('should not leak memory on repeated navigation', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act - Multiple navigation cycles
        for (int i = 0; i < 10; i++) {
          await tester.tap(find.text('Open Talker'));
          await tester.pumpAndSettle();
          await tester.pageBack();
          await tester.pumpAndSettle();
        }

        // Assert - Test completes without memory issues
        expect(find.text('Open Talker'), findsOneWidget);
      });
    });

    group('State Management', () {
      testWidgets('should maintain GetIt registration state correctly', (WidgetTester tester) async {
        // Arrange
        expect(GetIt.I.isRegistered<Talker>(), isFalse);

        GetIt.I.registerSingleton<Talker>(mockTalker);
        expect(GetIt.I.isRegistered<Talker>(), isTrue);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        expect(GetIt.I.isRegistered<Talker>(), isTrue);
        expect(find.byType(TalkerScreen), findsOneWidget);
      });

      testWidgets('should handle registration changes during widget lifecycle', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Initially no registration
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();
        expect(find.byType(TalkerScreen), findsNothing);

        // Register during widget lifecycle
        GetIt.I.registerSingleton<Talker>(mockTalker);

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(TalkerScreen), findsOneWidget);
      });
    });

    group('Navigation Behavior', () {
      testWidgets('should use MaterialPageRoute for navigation', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
            navigatorObservers: [mockNavigatorObserver],
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();

        // Assert
        final captured = verify(mockNavigatorObserver.didPush(captureAny, any)).captured;
        expect(captured.last, isA<MaterialPageRoute>());
      });

      testWidgets('should allow back navigation from TalkerScreen', (WidgetTester tester) async {
        // Arrange
        GetIt.I.registerSingleton<Talker>(mockTalker);

        await tester.pumpWidget(
          MaterialApp(
            home: const TestWidgetWithTalkerMixin(),
          ),
        );

        // Act
        await tester.tap(find.text('Open Talker'));
        await tester.pumpAndSettle();
        
        expect(find.byType(TalkerScreen), findsOneWidget);
        
        await tester.pageBack();
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(TalkerScreen), findsNothing);
        expect(find.text('Open Talker'), findsOneWidget);
      });
    });
  });
}
