<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info</title>
  <link rel="stylesheet" type="text/css" href="gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue">top level</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">39.0&nbsp;%</td>
            <td class="headerCovTableEntry">751</td>
            <td class="headerCovTableEntry">293</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 09:57:39</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <center>
          <table width="80%" cellpadding=1 cellspacing=1 border=0>

            <tr>
              <td width="40%"><br></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            </tr>

            <tr>
              <td class="tableHead" rowspan=2>Directory <span  title="Click to sort table by file name" class="tableHeadSort"><a href="index.html"><img src="updown.png" width=10 height=14 alt="Sort by file name" title="Click to sort table by file name" border=0></a></span></td>
        <td class="tableHead" colspan=4>Line Coverage <span  title="Click to sort table by line coverage" class="tableHeadSort"><a href="index-sort-l.html"><img src="updown.png" width=10 height=14 alt="Sort by line coverage" title="Click to sort table by line coverage" border=0></a></span></td>
            </tr>
            <tr>
                    <td class="tableHead" colspan=2> Rate</td>
                    <td class="tableHead"> Total</td>
                    <td class="tableHead"> Hit</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="data/data_source/remote/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/data_source/remote">data/data_source/remote/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">31</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="data/model/auth/request/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/model/auth/request">data/model/auth/request/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">12</td>
              <td class="coverNumDflt">12</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="data/model/auth/response/auth/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/model/auth/response/auth">data/model/auth/response/auth/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">14</td>
              <td class="coverNumDflt">14</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="data/repository/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/repository">data/repository/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">3</td>
              <td class="coverNumDflt">3</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="domain/entity/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity">domain/entity/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">4</td>
              <td class="coverNumDflt">4</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="domain/entity/test/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test">domain/entity/test/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=52 height=10 alt="51.7%"><img src="snow.png" width=48 height=10 alt="51.7%"></td></tr></table>
              </td>
              <td class="coverPerLo">51.7&nbsp;%</td>
              <td class="coverNumDflt">89</td>
              <td class="coverNumDflt">46</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="domain/usecase/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/usecase">domain/usecase/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">3</td>
              <td class="coverNumDflt">3</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="mapper/entity/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/mapper/entity">mapper/entity/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=22 height=10 alt="22.2%"><img src="snow.png" width=78 height=10 alt="22.2%"></td></tr></table>
              </td>
              <td class="coverPerLo">22.2&nbsp;%</td>
              <td class="coverNumDflt">167</td>
              <td class="coverNumDflt">37</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="mapper/entity/auth/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/mapper/entity/auth">mapper/entity/auth/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=31 height=10 alt="30.8%"><img src="snow.png" width=69 height=10 alt="30.8%"></td></tr></table>
              </td>
              <td class="coverPerLo">30.8&nbsp;%</td>
              <td class="coverNumDflt">91</td>
              <td class="coverNumDflt">28</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="navigator/model/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/navigator/model">navigator/model/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">1</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation">presentation/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">2</td>
              <td class="coverNumDflt">2</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/assignee/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/assignee">presentation/assignee/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">10</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/home/<USER>" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/home">presentation/home/<USER>/a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=61 height=10 alt="60.8%"><img src="snow.png" width=39 height=10 alt="60.8%"></td></tr></table>
              </td>
              <td class="coverPerLo">60.8&nbsp;%</td>
              <td class="coverNumDflt">125</td>
              <td class="coverNumDflt">76</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/login/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/login">presentation/login/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=95 height=10 alt="95.2%"><img src="snow.png" width=5 height=10 alt="95.2%"></td></tr></table>
              </td>
              <td class="coverPerHi">95.2&nbsp;%</td>
              <td class="coverNumDflt">21</td>
              <td class="coverNumDflt">20</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/shared/talker/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/shared/talker">presentation/shared/talker/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">5</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/test/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/test">presentation/test/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=10 height=10 alt="10.0%"><img src="snow.png" width=90 height=10 alt="10.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">10.0&nbsp;%</td>
              <td class="coverNumDflt">60</td>
              <td class="coverNumDflt">6</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/test/bloc/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/test/bloc">presentation/test/bloc/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=49 height=10 alt="49.4%"><img src="snow.png" width=51 height=10 alt="49.4%"></td></tr></table>
              </td>
              <td class="coverPerLo">49.4&nbsp;%</td>
              <td class="coverNumDflt">85</td>
              <td class="coverNumDflt">42</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="presentation/user/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/user">presentation/user/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">28</td>
              <td class="coverNumDflt"></td>
            </tr>
        <tr>
          <td class="footnote" colspan=5>Note:  'Function Coverage' columns elided as function owner is not identified.</td>
         </tr>
          </table>
          </center>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
